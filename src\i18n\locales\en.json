{"common": {"loading": "Loading...", "error": "Error", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "close": "Close", "search": "Search", "filter": "Filter", "clear": "Clear", "apply": "Apply", "reset": "Reset", "submit": "Submit", "back": "Back", "next": "Next", "previous": "Previous", "yes": "Yes", "no": "No", "ok": "OK", "na": "N/A", "total": "Total", "upcoming": "Upcoming", "completed": "Completed", "required": "Required", "notRequired": "Not Required", "goBack": "Go Back", "day": "day | days"}, "navigation": {"home": "Home", "matches": "Matches", "tournaments": "Tournaments", "about": "About", "login": "<PERSON><PERSON>", "logout": "Logout", "profile": "Profile", "settings": "Settings", "search": "Search", "mySignups": "My Signups", "watched": "Watched", "results": "Results", "archive": "Archive", "statistics": "Statistics", "favourites": "Favourites", "messages": "Messages"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "signIn": "Sign In", "signUp": "Sign Up", "withdraw": "Withdraw", "orContinueWith": "Or continue with", "googleNotImplemented": "Google (Not Implemented)", "facebookNotImplemented": "Facebook (Not Implemented)", "enterEmailPassword": "Enter your email and password to access your account.", "byClickingContinue": "By clicking continue, you agree to our", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy"}, "app": {"title": "Archery Points", "tagline": "Track your scores, climb the ranks, and become a champion.", "teamName": "The Archery Points Team"}, "views": {"about": "about", "settings": {"title": "Application Settings", "description": "This is the settings page. General application preferences and configurations will be managed here."}, "account": {"title": "Account <PERSON><PERSON>", "description": "This is the account page. User-specific settings and profile information will be displayed and managed here."}, "myMatches": {"title": "My Matches", "description": "Competitions you've signed up for", "loadingMatches": "Loading your matches...", "errorLoadingMatches": "Error loading matches", "noMatchesYet": "No matches yet", "noMatchesDescription": "You haven't signed up for any competitions yet.", "browseMatches": "Browse Matches", "upcomingMatches": "Upcoming Matches", "pastMatches": "Past Matches"}}, "matches": {"signUp": "SIGN UP", "withdraw": "WITHDRAW", "loading": "LOADING...", "loadingMatchDetails": "Loading match details...", "errorLoadingMatch": "Error loading match", "viewFullDetails": "View Full Details", "competitorsRivals": "Competitors/Rivals", "equipmentCategories": "Equipment Categories", "ageCategories": "Age Categories", "info": "Info", "organizer": "Organizer", "competitionRank": "Competition Rank", "competitionType": "Competition Type", "federation": "Federation", "license": "License", "phone": "Phone", "email": "Email", "distanceToEvent": "Distance to event", "sunrise": "Sunrise", "sunset": "Sunset", "temperature": "Temperature", "weather": "Weather", "min": "Min", "max": "Max", "age": "Age", "ageRange": "Age: {min} - {max}", "ageMin": "Age: {min}+", "ageMax": "Age: up to {max}", "createMatch": "Create Match", "basicInfo": "Basic Information", "locationInfo": "Location", "divisionsInfo": "Divisions", "detailsInfo": "Details", "agendaInfo": "Agenda", "settingsInfo": "Settings", "step": "Step", "of": "of", "name": "Name", "startDate": "Start Date", "endDate": "End Date", "coverImageUrl": "Cover Image URL", "description": "Description", "matchType": "Match Type", "address": "Address", "city": "City", "country": "Country", "postcode": "Postcode", "judges": "Judges (one per line)", "addAgendaItem": "Add Agenda Item", "removeAgendaItem": "Remove", "agendaTime": "Time", "agendaActivity": "Activity", "licenseRequired": "License Required", "currency": "<PERSON><PERSON><PERSON><PERSON>", "competitionLevel": "Competition Level", "international": "International", "withoutLimits": "Without Limits", "publishAt": "Publish At", "registrationEnds": "Registration Ends", "maxPlayersAmount": "Max Players", "matchCreated": "Match created successfully", "matchCreationFailed": "Failed to create match", "selectFederation": "Select Federation", "optional": "Optional", "addJudge": "Add Judge", "removeJudge": "Remove Judge", "agendaName": "Name", "agendaLocation": "Location", "agendaDate": "Date", "agendaGoals": "Goals", "agendaArrows": "Number of Arrows", "agendaTypesOfGoals": "Types of Goals", "agendaBranch": "Competition Branch", "ageDivisions": "Age Divisions", "styleDivisions": "Style Divisions", "addAgeDivision": "Add Age Division", "addStyleDivision": "Add Style Division", "noAgeDivisions": "No age divisions added yet", "noStyleDivisions": "No style divisions added yet", "selectAgeDivision": "Select Age Division", "selectStyleDivision": "Select Style Division", "ageDivision": "Age Division", "styleDivision": "Style Division", "shortName": "Short Name", "minAge": "Min Age", "maxAge": "Max Age", "isOpen": "Open (both genders)", "federationDivisionsInfo": "Using divisions from {federation} federation", "pricingInfo": "Pricing", "noPricingAvailable": "No pricing available", "addAgeDivisionsFirst": "Add age divisions first to set pricing", "pricingByAgeDivision": "Pricing by Age Division", "pricingDescription": "Set entry fees for each age division", "entryFee": "Entry Fee", "enterPrice": "Enter price", "pricingSummary": "Pricing Summary"}, "filters": {"toggleFilters": "Toggle filters", "thisWeek": "This week", "nextMonth": "Next month", "next3Months": "Next 3 months", "next6Months": "Next 6 months", "thisYear": "This year", "nearMe": "Near me", "available": "Available", "tournaments": "Tournaments", "active": "Active", "completed": "Completed"}, "calendar": {}, "tournament": {"detailsTitle": "Tournament Details", "loading": "Loading tournament details...", "error": "Error loading tournament details", "created": "Created", "completed": "Completed", "federation": "Federation", "organizer": "Organizer", "description": "Description", "upcomingMatches": "Upcoming Matches", "pastMatches": "Past Matches", "players": "Players", "playerId": "ID: {id}", "notFound": "Tournament not found", "selectToView": "Select a tournament to view details", "rounds": "Rounds", "round": "Round", "viewDetails": "View Details", "matches": "Matches", "noMatches": "No matches found", "totalMatches": "{count} total matches", "ongoing": "Ongoing", "ended": "Ended", "past": "Past", "future": "Future", "missing": "Missing", "registered": "Registered"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "url": "Please enter a valid URL", "min": "Minimum value is {min}", "max": "Maximum value is {max}", "minLength": "Minimum length is {min} characters", "maxLength": "Maximum length is {max} characters", "formHasErrors": "Please fix the errors in the form before submitting"}}